# 高德地图配置指南

## 1. 申请高德地图API密钥

### 步骤1：注册高德开发者账号
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 点击右上角"注册/登录"
3. 完成账号注册和实名认证

### 步骤2：创建应用
1. 登录后进入 [控制台](https://console.amap.com/)
2. 点击"应用管理" -> "我的应用"
3. 点击"创建新应用"
4. 填写应用信息：
   - 应用名称：贝宠乐福员工端
   - 应用类型：移动应用

### 步骤3：添加Key
1. 在应用详情页点击"添加Key"
2. 填写Key信息：
   - Key名称：小程序Key
   - 服务平台：微信小程序
   - PackageName：您的小程序AppID
3. 点击"提交"

### 步骤4：获取API密钥
创建成功后，您将获得一个API密钥（Key），类似：`a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`

## 2. 配置项目

### 步骤1：更新MapConfig.js
将您获得的API密钥填入 `utils/MapConfig.js` 文件：

```javascript
export default {
  // 高德地图API密钥
  AMAP_KEY: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6', // 替换为您的实际密钥
  
  // 其他配置保持不变...
};
```

### 步骤2：验证配置
在小程序中测试地址转换功能：

```javascript
import AddressUtils from '../utils/AddressUtils';

// 测试经纬度转地址
const address = await AddressUtils.getAddressFromLocation(39.9042, 116.4074);
console.log('地址:', address); // 应该输出北京的地址信息

// 测试地址转经纬度
const location = await AddressUtils.getLocationFromAddress('北京市朝阳区');
console.log('坐标:', location); // 应该输出 {lat: 39.xxx, lng: 116.xxx}
```

## 3. 常见问题

### Q1: API调用失败，返回错误码
**可能原因：**
- API密钥无效或过期
- 小程序AppID与申请时填写的不一致
- API调用配额用完

**解决方案：**
1. 检查API密钥是否正确
2. 确认小程序AppID与高德控制台中的一致
3. 查看高德控制台的调用统计和配额

### Q2: 地址解析不准确
**可能原因：**
- 经纬度坐标系不匹配
- 地址信息不完整

**解决方案：**
1. 确保使用GCJ02坐标系（国测局坐标系）
2. 提供更详细的地址信息

### Q3: 小程序域名配置
确保在小程序管理后台的"开发设置"中添加了高德地图域名：
- `https://restapi.amap.com`

## 4. API配额说明

高德地图API有免费配额限制：
- 个人开发者：每日10,000次调用
- 企业开发者：每日100,000次调用

如果超出配额，需要购买商业授权。

## 5. 技术支持

如果遇到问题，可以：
1. 查看 [高德地图API文档](https://lbs.amap.com/api/wx/summary)
2. 访问 [高德开发者论坛](https://lbs.amap.com/dev/forum)
3. 联系高德技术支持

## 6. 迁移完成检查

完成配置后，请检查以下功能是否正常：
- [ ] 员工打卡页面的位置获取
- [ ] 订单页面的导航功能
- [ ] 历史记录中的地址显示
- [ ] 其他涉及地址转换的功能

如果所有功能正常，说明高德地图配置成功！
