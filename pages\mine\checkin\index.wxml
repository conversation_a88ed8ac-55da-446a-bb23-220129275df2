<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">出车拍照</text>
    <view class="header-actions" wx:if="{{!isForceMode}}">
      <text class="history-btn" bind:tap="viewHistory">历史记录</text>
    </view>
  </view>

  <!-- 强制打卡提示 -->
  <view wx:if="{{isForceMode}}" class="force-checkin-notice">
    <view class="notice-icon">⚠️</view>
    <view class="notice-content">
      <text class="notice-title">强制打卡提醒</text>
      <text class="notice-desc">您已超过一周未进行出车打卡，请立即完成打卡</text>
    </view>
  </view>

  <!-- 今日统计 -->
  <view class="stats-section">
    <view class="stats-card" bind:tap="viewStatistics">
      <view class="stats-item">
        <text class="stats-number">{{statistics.todayCount || 0}}</text>
        <text class="stats-label">今日打卡</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{statistics.totalCount || 0}}</text>
        <text class="stats-label">累计打卡</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">-</text>
        <text class="stats-label">本月打卡</text>
        <text class="stats-note">暂未统计</text>
      </view>
      <view class="stats-tip">
        <text class="tip-text">点击查看详细统计</text>
        <text class="tip-arrow">→</text>
      </view>
    </view>
  </view>

  <!-- 拍照区域 -->
  <view class="photo-section">
    <view class="section-title">
      <text class="title-text">拍照打卡</text>
      <text class="title-desc">每类最多可上传9张照片</text>
    </view>

    <!-- 照片类型切换 -->
    <view class="photo-type-tabs">
      <view
        class="tab-item {{activePhotoType === 'vehicleExterior' ? 'active' : ''}}"
        data-type="vehicleExterior"
        bind:tap="switchPhotoType"
      >
        <text class="tab-text">车辆外观</text>
        <text class="tab-count">({{photos.vehicleExterior.length}}/9)</text>
      </view>
      <view
        class="tab-item {{activePhotoType === 'serviceStaff' ? 'active' : ''}}"
        data-type="serviceStaff"
        bind:tap="switchPhotoType"
      >
        <text class="tab-text">服务人员</text>
        <text class="tab-count">({{photos.serviceStaff.length}}/9)</text>
      </view>
      <view
        class="tab-item {{activePhotoType === 'vehicleInterior' ? 'active' : ''}}"
        data-type="vehicleInterior"
        bind:tap="switchPhotoType"
      >
        <text class="tab-text">车内情况</text>
        <text class="tab-count">({{photos.vehicleInterior.length}}/9)</text>
      </view>
    </view>

    <!-- 当前类型的照片网格 -->
    <view class="photo-grid">
      <view
        wx:for="{{photos[activePhotoType]}}"
        wx:key="index"
        class="photo-item"
        data-url="{{item}}"
        data-type="{{activePhotoType}}"
        bind:tap="previewImage"
      >
        <image src="{{item}}" class="photo-image" mode="aspectFill"></image>
        <view
          class="photo-delete"
          data-index="{{index}}"
          data-type="{{activePhotoType}}"
          bind:tap="deletePhoto"
          catch:tap="deletePhoto"
        >
          <text class="delete-icon">×</text>
        </view>
      </view>

      <view
        wx:if="{{photos[activePhotoType].length < 9}}"
        class="photo-add"
        bind:tap="chooseImage"
      >
        <text class="add-icon">+</text>
        <text class="add-text">添加照片</text>
      </view>
    </view>

    <view wx:if="{{uploading}}" class="uploading-tip">
      <text>照片上传中...</text>
    </view>
  </view>

  <!-- 描述输入 -->
  <view class="description-section">
    <view class="section-title">
      <text class="title-text">打卡描述</text>
      <text class="title-desc">{{description.length}}/500</text>
    </view>
    <textarea 
      class="description-input"
      placeholder="请输入打卡描述（必填）"
      value="{{description}}"
      maxlength="500"
      bind:input="onDescriptionInput"
    ></textarea>
  </view>

  <!-- 位置信息 -->
  <view class="location-section">
    <view class="section-title">
      <text class="title-text">位置信息</text>
      <text class="title-desc">可选</text>
    </view>
    
    <view wx:if="{{!location}}" class="location-empty">
      <button class="location-btn" bind:tap="getLocation">
        <text class="location-icon">📍</text>
        <text>获取当前位置</text>
      </button>
    </view>
    
    <view wx:else class="location-info">
      <view class="location-text">
        <text class="location-address">{{location.address}}</text>
      </view>
      <button class="location-clear" bind:tap="clearLocation">清除</button>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{canSubmit ? 'active' : ''}}"
      bind:tap="submitCheckin"
    >
      提交打卡
    </button>
  </view>

  <!-- 今日记录 -->
  <view wx:if="{{todayCheckins.length > 0}}" class="today-section">
    <view class="section-title">
      <text class="title-text">今日记录</text>
      <text class="title-desc">{{todayCheckins.length}}条</text>
    </view>
    
    <view class="checkin-list">
      <view 
        wx:for="{{todayCheckins}}" 
        wx:key="id" 
        class="checkin-item"
      >
        <view class="checkin-time">{{item.checkInTime}}</view>
        <view class="checkin-desc">{{item.description}}</view>
        <view wx:if="{{item.address}}" class="checkin-location">📍 {{item.address}}</view>

        <!-- 分组显示照片 -->
        <view class="checkin-photos-groups">
          <view wx:if="{{item.photos.vehicleExterior && item.photos.vehicleExterior.length > 0}}" class="photo-group">
            <text class="group-title">车辆外观 ({{item.photos.vehicleExterior.length}}张)</text>
            <view class="photo-list">
              <image
                wx:for="{{item.photos.vehicleExterior}}"
                wx:for-item="photo"
                wx:key="index"
                src="{{photo}}"
                class="checkin-photo"
                mode="aspectFill"
                data-url="{{photo}}"
                bind:tap="previewImage"
              ></image>
            </view>
          </view>

          <view wx:if="{{item.photos.serviceStaff && item.photos.serviceStaff.length > 0}}" class="photo-group">
            <text class="group-title">服务人员 ({{item.photos.serviceStaff.length}}张)</text>
            <view class="photo-list">
              <image
                wx:for="{{item.photos.serviceStaff}}"
                wx:for-item="photo"
                wx:key="index"
                src="{{photo}}"
                class="checkin-photo"
                mode="aspectFill"
                data-url="{{photo}}"
                bind:tap="previewImage"
              ></image>
            </view>
          </view>

          <view wx:if="{{item.photos.vehicleInterior && item.photos.vehicleInterior.length > 0}}" class="photo-group">
            <text class="group-title">车内情况 ({{item.photos.vehicleInterior.length}}张)</text>
            <view class="photo-list">
              <image
                wx:for="{{item.photos.vehicleInterior}}"
                wx:for-item="photo"
                wx:key="index"
                src="{{photo}}"
                class="checkin-photo"
                mode="aspectFill"
                data-url="{{photo}}"
                bind:tap="previewImage"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>


</view>
