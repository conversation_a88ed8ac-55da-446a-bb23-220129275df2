/**
 * 地址工具类
 * 提供经纬度转地址、地址转经纬度等功能
 * 使用高德地图API
 */

// 引入高德地图SDK
import { AMapWX } from '../libs/amap-wx.130.js';
// 引入地图配置
import MapConfig from './MapConfig.js';

// 初始化高德地图实例
let amapInstance = null;

// 获取高德地图实例
function getAmapInstance() {
  if (!amapInstance) {
    amapInstance = new AMapWX({
      key: MapConfig.AMAP_KEY
    });
  }
  return amapInstance;
}

class AddressUtils {
  /**
   * 根据经纬度获取地址信息（逆地理编码）
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {object} options 选项
   * @param {boolean} options.showLoading 是否显示加载提示，默认false
   * @param {boolean} options.getPoi 是否获取POI信息，默认true
   * @returns {Promise<string>} 地址字符串
   */
  static getAddressFromLocation(latitude, longitude, options = {}) {
    const { showLoading = false } = options;

    return new Promise((resolve) => {
      if (!latitude || !longitude) {
        resolve('');
        return;
      }

      if (showLoading) {
        wx.showLoading({
          title: '获取地址中...'
        });
      }

      const amap = getAmapInstance();

      amap.getRegeo({
        location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
        success: (data) => {
          if (showLoading) {
            wx.hideLoading();
          }

          let address = '';
          if (data && data.length > 0) {
            const result = data[0];
            // 使用高德地图返回的格式化地址
            address = result.name || result.desc || '';
          }
          resolve(address);
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          console.error('AddressUtils: 高德地图地址解析失败', error);
          resolve('');
        }
      });
    });
  }

  /**
   * 根据地址获取经纬度信息（地理编码）
   * @param {string} address 地址
   * @param {object} options 选项
   * @param {boolean} options.showLoading 是否显示加载提示，默认false
   * @returns {Promise<object|null>} 包含lat和lng的对象，失败返回null
   */
  static getLocationFromAddress(address, options = {}) {
    const { showLoading = false } = options;

    return new Promise((resolve) => {
      if (!address) {
        resolve(null);
        return;
      }

      if (showLoading) {
        wx.showLoading({
          title: '搜索地址中...'
        });
      }

      const amap = getAmapInstance();

      amap.getGeo({
        options: {
          address: address
        },
        success: (data) => {
          if (showLoading) {
            wx.hideLoading();
          }

          if (data && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location;
            if (location) {
              // 高德地图返回格式：经度,纬度
              const [lng, lat] = location.split(',').map(parseFloat);
              resolve({ lat, lng });
            } else {
              resolve(null);
            }
          } else {
            resolve(null);
          }
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          console.error('AddressUtils: 高德地图地址搜索失败', error);
          resolve(null);
        }
      });
    });
  }

  /**
   * 格式化地址显示
   * 优先显示地址，如果没有地址但有经纬度则显示"位置信息"
   * @param {object} item 包含地址和经纬度信息的对象
   * @param {string} item.address 地址
   * @param {number} item.latitude 纬度
   * @param {number} item.longitude 经度
   * @returns {string} 格式化后的地址显示
   */
  static formatAddressDisplay(item) {
    if (!item) return '';
    
    // 如果有地址信息，直接使用（排除错误信息）
    if (item.address && 
        item.address !== '位置获取失败' && 
        item.address !== '位置解析失败' &&
        item.address !== '') {
      return item.address;
    }
    
    // 如果有经纬度但没有地址，显示"位置信息"
    if (item.latitude && item.longitude) {
      return '位置信息';
    }
    
    // 都没有则返回空
    return '';
  }

  /**
   * 实时获取并显示地址
   * 根据经纬度实时获取地址并更新显示
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {function} callback 回调函数，参数为获取到的地址
   * @param {object} options 选项
   * @returns {Promise<string>} 地址字符串
   */
  static async getRealTimeAddress(latitude, longitude, callback, options = {}) {
    try {
      const address = await this.getAddressFromLocation(latitude, longitude, options);
      
      if (address && callback && typeof callback === 'function') {
        callback(address);
      }
      
      return address || '位置获取失败';
    } catch (error) {
      console.error('AddressUtils: 实时获取地址失败', error);
      const fallbackAddress = '位置获取失败';
      
      if (callback && typeof callback === 'function') {
        callback(fallbackAddress);
      }
      
      return fallbackAddress;
    }
  }

  /**
   * 批量转换地址
   * 将包含经纬度的数据列表批量转换为包含地址的列表
   * @param {Array} dataList 数据列表
   * @param {object} options 选项
   * @param {string} options.latField 纬度字段名，默认'latitude'
   * @param {string} options.lngField 经度字段名，默认'longitude'
   * @param {string} options.addressField 地址字段名，默认'address'
   * @param {number} options.delay 请求间隔（毫秒），默认100ms
   * @returns {Promise<Array>} 转换后的数据列表
   */
  static async batchConvertAddress(dataList, options = {}) {
    const { 
      latField = 'latitude', 
      lngField = 'longitude', 
      addressField = 'address',
      delay = 100 
    } = options;
    
    if (!Array.isArray(dataList) || dataList.length === 0) {
      return dataList;
    }

    const results = [];
    
    for (let i = 0; i < dataList.length; i++) {
      const item = { ...dataList[i] };
      
      // 如果已经有地址且不是错误信息，跳过转换
      if (item[addressField] && 
          item[addressField] !== '位置获取失败' && 
          item[addressField] !== '位置解析失败') {
        results.push(item);
        continue;
      }
      
      // 如果有经纬度，尝试获取地址
      if (item[latField] && item[lngField]) {
        try {
          const address = await this.getAddressFromLocation(item[latField], item[lngField]);
          if (address) {
            item[addressField] = address;
          }
        } catch (error) {
          console.error(`AddressUtils: 批量转换第${i}项失败`, error);
        }
        
        // 添加延迟避免请求过于频繁
        if (delay > 0 && i < dataList.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      results.push(item);
    }
    
    return results;
  }

  /**
   * 打开导航
   * 使用微信内置地图打开导航
   * @param {object} location 位置信息
   * @param {string} location.address 地址
   * @param {string} location.remark 备注
   * @param {number} location.latitude 纬度
   * @param {number} location.longitude 经度
   */
  static async openNavigation(location) {
    const { address, remark, latitude, longitude } = location;

    if (!address) {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'none'
      });
      return;
    }

    // 构建完整地址
    const fullAddress = remark ? `${address}(${remark})` : address;

    // 优先使用订单中的经纬度数据
    if (latitude && longitude) {
      const lat = parseFloat(latitude);
      const lng = parseFloat(longitude);

      // 验证经纬度数据的有效性
      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        wx.openLocation({
          latitude: lat,
          longitude: lng,
          name: fullAddress,
          address: fullAddress,
          scale: 18
        });
        return;
      }
    }

    // 如果没有经纬度数据或数据无效，则使用地址搜索
    try {
      const locationResult = await this.getLocationFromAddress(fullAddress, { showLoading: true });
      
      if (locationResult) {
        wx.openLocation({
          latitude: locationResult.lat,
          longitude: locationResult.lng,
          name: fullAddress,
          address: fullAddress,
          scale: 18
        });
      } else {
        wx.showToast({
          title: '无法找到该地址',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('AddressUtils: 导航失败', error);
      wx.showToast({
        title: '导航功能暂时不可用',
        icon: 'none'
      });
    }
  }
}

export default AddressUtils;
