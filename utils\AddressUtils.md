# AddressUtils 地址工具类使用说明

## 概述

`AddressUtils` 是一个统一的地址处理工具类，提供经纬度转地址、地址转经纬度、地址格式化显示等功能。基于高德地图API，所有涉及地址转换的页面都应该使用这个公共工具，避免重复代码。

## 配置说明

在使用前，请先配置高德地图API密钥：

1. 前往 [高德开放平台](https://console.amap.com/dev/key/app) 申请小程序应用的API密钥
2. 修改 `utils/MapConfig.js` 文件中的 `AMAP_KEY` 字段：

```javascript
export default {
  // 高德地图API密钥
  AMAP_KEY: '您的高德地图API密钥',
  // ...其他配置
};
```

3. 确保 `app.json` 中已添加高德地图域名：

```json
{
  "domainList": ["restapi.amap.com"]
}
```

## 主要功能

### 1. 经纬度转地址（逆地理编码）

```javascript
import AddressUtils from '../../utils/AddressUtils';

// 基本用法
const address = await AddressUtils.getAddressFromLocation(latitude, longitude);

// 带选项的用法
const address = await AddressUtils.getAddressFromLocation(latitude, longitude, {
  showLoading: true,  // 显示加载提示
  getPoi: true        // 获取POI信息
});
```

### 2. 地址转经纬度（地理编码）

```javascript
// 根据地址获取经纬度
const location = await AddressUtils.getLocationFromAddress('北京市朝阳区', {
  showLoading: true
});

if (location) {
  console.log('纬度:', location.lat);
  console.log('经度:', location.lng);
}
```

### 3. 格式化地址显示

```javascript
// 格式化地址显示，优先显示地址，没有地址时显示"位置信息"
const displayAddress = AddressUtils.formatAddressDisplay({
  address: '北京市朝阳区',
  latitude: 39.9042,
  longitude: 116.4074
});
```

### 4. 实时获取并显示地址

```javascript
// 实时获取地址并通过回调更新界面
const address = await AddressUtils.getRealTimeAddress(
  latitude, 
  longitude, 
  (address) => {
    // 回调函数，地址获取成功后调用
    this.setData({ currentAddress: address });
  }
);
```

### 5. 批量转换地址

```javascript
// 批量将包含经纬度的数据转换为包含地址的数据
const dataWithAddress = await AddressUtils.batchConvertAddress(dataList, {
  latField: 'latitude',     // 纬度字段名
  lngField: 'longitude',    // 经度字段名
  addressField: 'address',  // 地址字段名
  delay: 100               // 请求间隔（毫秒）
});
```

### 6. 打开导航

```javascript
// 使用微信内置地图打开导航
AddressUtils.openNavigation({
  address: '北京市朝阳区',
  remark: '近地铁站',
  latitude: 39.9042,
  longitude: 116.4074
});
```

## 在页面中的使用示例

### 1. 打卡页面 - 位置获取和显示

```javascript
// pages/mine/checkin/index.js
import AddressUtils from '../../../utils/AddressUtils';

Page({
  // 获取位置并转换为地址
  async getLocationData() {
    wx.getLocation({
      type: 'gcj02',
      success: async (res) => {
        const { latitude, longitude } = res;
        
        // 使用公共工具获取地址
        const address = await AddressUtils.getAddressFromLocation(latitude, longitude, {
          showLoading: true
        });
        
        this.setData({
          location: {
            latitude,
            longitude,
            address: address || '位置获取失败'
          }
        });
      }
    });
  },

  // 格式化今日记录的地址显示
  async loadTodayCheckins() {
    const result = await checkinApi.getTodayList(this.data.userInfo.id);
    if (result && result.list) {
      const formattedList = result.list.map(item => ({
        ...item,
        displayAddress: AddressUtils.formatAddressDisplay(item)
      }));
      
      this.setData({ todayCheckins: formattedList });
    }
  }
});
```

### 2. 订单页面 - 导航功能

```javascript
// pages/index/index.js
import AddressUtils from '../../utils/AddressUtils';

Page({
  // 打开导航
  openNavigation(e) {
    const { address, remark, latitude, longitude } = e.currentTarget.dataset;
    
    // 使用公共工具打开导航
    AddressUtils.openNavigation({
      address,
      remark,
      latitude,
      longitude
    });
  }
});
```

### 3. 历史记录页面 - 批量地址转换

```javascript
// pages/mine/checkin/history/index.js
import AddressUtils from '../../../../utils/AddressUtils';

Page({
  // 加载记录并批量转换地址
  async loadCheckinList() {
    const result = await checkinApi.getList(this.data.userInfo.id, params);
    
    if (result && result.list) {
      // 批量转换地址
      const listWithAddress = await AddressUtils.batchConvertAddress(result.list, {
        delay: 50 // 减少请求间隔
      });
      
      this.setData({ checkinList: listWithAddress });
    }
  }
});
```

## 模板中的使用

### WXML 模板

```xml
<!-- 显示格式化后的地址 -->
<view wx:if="{{item.displayAddress}}" class="location">
  📍 {{item.displayAddress}}
</view>

<!-- 导航按钮 -->
<view 
  class="nav-btn" 
  bindtap="openNavigation"
  data-address="{{item.address}}"
  data-latitude="{{item.latitude}}"
  data-longitude="{{item.longitude}}"
>
  导航
</view>
```

## 注意事项

1. **API限制**: 高德地图API有调用频率限制，批量转换时建议设置适当的延迟
2. **错误处理**: 所有方法都有内置的错误处理，失败时会返回空字符串或null
3. **缓存策略**: 如果需要缓存地址信息，可以在业务层实现
4. **权限检查**: 获取位置前请确保已获得用户授权
5. **API密钥**: 请确保高德地图API密钥有效且有足够的调用配额

## 迁移指南

### 从旧代码迁移

如果你的页面中有类似以下的代码：

```javascript
// 旧代码 - 需要替换（腾讯地图）
wx.request({
  url: 'https://apis.map.qq.com/ws/geocoder/v1/',
  data: {
    location: `${latitude},${longitude}`,
    key: 'OQRBZ-KZXKF-XEEJJ-YNVQQ-QZJQS-XQFQH'
  },
  success: (res) => {
    // 处理地址...
  }
});
```

替换为：

```javascript
// 新代码 - 使用高德地图公共工具
const address = await AddressUtils.getAddressFromLocation(latitude, longitude);
```

这样可以：
- 减少重复代码
- 统一错误处理
- 便于维护和更新
- 提供更好的用户体验
