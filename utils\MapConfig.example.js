/**
 * 地图配置文件示例
 * 请复制此文件为 MapConfig.js 并填入您的实际配置
 */

export default {
  // 高德地图API密钥
  // 请在高德开放平台申请小程序应用的API密钥
  // https://console.amap.com/dev/key/app
  AMAP_KEY: 'YOUR_AMAP_KEY_HERE',
  
  // 地图服务配置
  MAP_CONFIG: {
    // 坐标系类型
    coordType: 'gcj02',
    
    // 默认缩放级别
    defaultZoom: 18,
    
    // 逆地理编码扩展信息
    extensions: 'all',
    
    // 批量请求间隔（毫秒）
    batchDelay: 100,
    
    // 请求超时时间（毫秒）
    timeout: 10000
  },
  
  // 错误信息配置
  ERROR_MESSAGES: {
    LOCATION_PERMISSION_DENIED: '位置权限未授权',
    LOCATION_UNAVAILABLE: '位置信息不可用',
    ADDRESS_NOT_FOUND: '无法找到该地址',
    NETWORK_ERROR: '网络错误，请检查网络连接',
    API_KEY_INVALID: 'API密钥无效',
    SERVICE_UNAVAILABLE: '地图服务暂时不可用'
  }
};
